#!/usr/bin/env python3
"""
Test wszystkich API endpoint dla wykresów.
"""

import requests
import json

def test_all_chart_apis():
    """Test wszystkich API endpoint dla wykresów."""
    
    print("🔍 TEST WSZYSTKICH API ENDPOINT DLA WYKRESÓW")
    print("=" * 50)
    
    endpoints = [
        ("/api/statistics", "Statistics"),
        ("/api/pnl-chart", "PnL Chart"),
        ("/api/pnl-distribution", "PnL Distribution"),
        ("/api/signals?limit=5", "Signals"),
        ("/api/pairs", "Pairs")
    ]
    
    for endpoint, name in endpoints:
        print(f"\n📊 {name} ({endpoint}):")
        try:
            response = requests.get(f'http://localhost:5000{endpoint}')
            if response.status_code == 200:
                data = response.json()
                
                if isinstance(data, list):
                    print(f"  ✅ Status: OK - Array with {len(data)} items")
                    if len(data) > 0:
                        print(f"  📋 First item keys: {list(data[0].keys())}")
                        if name == "PnL Chart" and len(data) > 0:
                            item = data[0]
                            print(f"  📈 Sample data: date={item.get('date')}, cumulative_pnl={item.get('cumulative_pnl')}")
                        elif name == "PnL Distribution" and len(data) > 0:
                            item = data[0]
                            print(f"  📊 Sample data: range_start={item.get('range_start')}, count={item.get('count')}")
                    else:
                        print(f"  ⚠️  Empty array")
                        
                elif isinstance(data, dict):
                    print(f"  ✅ Status: OK - Object")
                    print(f"  📋 Keys: {list(data.keys())}")
                    if name == "Statistics":
                        print(f"  📈 Sample stats: total_signals={data.get('total_signals')}, win_rate={data.get('win_rate')}")
                        if 'pair_stats' in data:
                            pair_stats = data['pair_stats']
                            print(f"  📊 Pair stats: {len(pair_stats) if pair_stats else 0} pairs")
                else:
                    print(f"  ✅ Status: OK - {type(data).__name__}")
                    
            else:
                print(f"  ❌ Error: {response.status_code}")
                print(f"  📄 Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"  ❌ Exception: {e}")
    
    print(f"\n🎯 PODSUMOWANIE:")
    print("Sprawdź w przeglądarce:")
    print("1. Otwórz konsolę deweloperską (F12)")
    print("2. Sprawdź czy są błędy JavaScript")
    print("3. Sprawdź czy logi 'Updating dashboard with data' są wyświetlane")
    print("4. Sprawdź czy logi 'Updating PnL Distribution chart' są wyświetlane")

if __name__ == "__main__":
    test_all_chart_apis()
