<!DOCTYPE html>
<html lang="pl">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Discord Bybit Signal Monitor - Production Dashboard</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
      /* === RESET & BASE STYLES === */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      :root {
        /* Dark Trading Theme Colors */
        --bg-primary: #0a0e1a;
        --bg-secondary: #1a1f2e;
        --bg-tertiary: #252b3d;
        --bg-card: #1e2332;
        --bg-hover: #2a3142;

        /* Accent Colors */
        --accent-primary: #00d4aa;
        --accent-secondary: #0099ff;
        --accent-danger: #ff4757;
        --accent-warning: #ffa502;
        --accent-success: #2ed573;

        /* Text Colors */
        --text-primary: #ffffff;
        --text-secondary: #a0a9c0;
        --text-muted: #6c7293;

        /* Border Colors */
        --border-primary: #2a3142;
        --border-secondary: #3a4158;

        /* Gradients */
        --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        --gradient-danger: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
        --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

        /* Shadows */
        --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
        --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.15);
        --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.25);
        --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.35);

        /* Transitions */
        --transition-fast: 0.15s ease;
        --transition-normal: 0.3s ease;
        --transition-slow: 0.5s ease;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          Roboto, sans-serif;
        background: var(--bg-primary);
        color: var(--text-primary);
        line-height: 1.6;
        overflow-x: hidden;
      }

      /* === SCROLLBAR STYLING === */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: var(--bg-secondary);
      }

      ::-webkit-scrollbar-thumb {
        background: var(--accent-primary);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: var(--accent-secondary);
      }

      /* === UTILITY CLASSES === */
      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 20px;
      }

      .grid {
        display: grid;
        gap: 20px;
      }

      .grid-cols-1 {
        grid-template-columns: repeat(1, 1fr);
      }
      .grid-cols-2 {
        grid-template-columns: repeat(2, 1fr);
      }
      .grid-cols-3 {
        grid-template-columns: repeat(3, 1fr);
      }
      .grid-cols-4 {
        grid-template-columns: repeat(4, 1fr);
      }
      .grid-cols-6 {
        grid-template-columns: repeat(6, 1fr);
      }

      .flex {
        display: flex;
      }

      .flex-col {
        flex-direction: column;
      }

      .items-center {
        align-items: center;
      }

      .justify-between {
        justify-content: space-between;
      }

      .justify-center {
        justify-content: center;
      }

      .text-center {
        text-align: center;
      }

      .text-right {
        text-align: right;
      }

      .font-bold {
        font-weight: 600;
      }

      .font-semibold {
        font-weight: 500;
      }

      .text-sm {
        font-size: 0.875rem;
      }

      .text-lg {
        font-size: 1.125rem;
      }

      .text-xl {
        font-size: 1.25rem;
      }

      .text-2xl {
        font-size: 1.5rem;
      }

      .text-3xl {
        font-size: 1.875rem;
      }

      .mb-2 {
        margin-bottom: 0.5rem;
      }

      .mb-4 {
        margin-bottom: 1rem;
      }

      .mb-6 {
        margin-bottom: 1.5rem;
      }

      .mt-4 {
        margin-top: 1rem;
      }

      .p-4 {
        padding: 1rem;
      }

      .p-6 {
        padding: 1.5rem;
      }

      .px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
      }

      .py-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
      }

      .py-3 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
      }

      .rounded {
        border-radius: 8px;
      }

      .rounded-lg {
        border-radius: 12px;
      }

      .rounded-xl {
        border-radius: 16px;
      }

      .shadow-lg {
        box-shadow: var(--shadow-lg);
      }

      .transition {
        transition: var(--transition-normal);
      }

      /* === COMPONENT STYLES === */
      .navbar {
        background: var(--bg-card);
        border-bottom: 1px solid var(--border-primary);
        padding: 1rem 0;
        position: sticky;
        top: 0;
        z-index: 100;
      }

      .navbar-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .navbar-brand {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
      }

      .live-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--accent-success);
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% {
          box-shadow: 0 0 0 0 rgba(46, 213, 115, 0.7);
        }
        70% {
          box-shadow: 0 0 0 10px rgba(46, 213, 115, 0);
        }
        100% {
          box-shadow: 0 0 0 0 rgba(46, 213, 115, 0);
        }
      }

      .btn {
        padding: 8px 16px;
        border-radius: 8px;
        border: none;
        font-weight: 500;
        cursor: pointer;
        transition: var(--transition-normal);
        display: inline-flex;
        align-items: center;
        gap: 8px;
      }

      .btn-primary {
        background: var(--gradient-primary);
        color: white;
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .btn-outline {
        background: transparent;
        border: 1px solid var(--border-secondary);
        color: var(--text-secondary);
      }

      .btn-outline:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
      }

      .btn-outline.active {
        background: var(--accent-primary);
        color: white;
        border-color: var(--accent-primary);
      }

      .card {
        background: var(--bg-card);
        border-radius: 16px;
        border: 1px solid var(--border-primary);
        box-shadow: var(--shadow-md);
        transition: var(--transition-normal);
        overflow: hidden;
      }

      .card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
      }

      .card-header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid var(--border-primary);
        background: var(--bg-tertiary);
      }

      .card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .card-body {
        padding: 24px;
      }

      /* === STAT CARDS === */
      .stat-card {
        background: var(--bg-card);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid var(--border-primary);
        position: relative;
        overflow: hidden;
        transition: var(--transition-normal);
      }

      .stat-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
      }

      .stat-card.success::before {
        background: var(--gradient-success);
      }

      .stat-card.danger::before {
        background: var(--gradient-danger);
      }

      .stat-card.warning::before {
        background: var(--gradient-warning);
      }

      .stat-card.info::before {
        background: var(--gradient-info);
      }

      .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-xl);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 16px;
        background: var(--bg-tertiary);
        color: var(--accent-primary);
      }

      .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        font-weight: 500;
      }

      .stat-change {
        font-size: 0.75rem;
        font-weight: 600;
        margin-top: 8px;
      }

      .stat-change.positive {
        color: var(--accent-success);
      }

      .stat-change.negative {
        color: var(--accent-danger);
      }

      /* === CHART CONTAINERS === */
      .chart-container {
        position: relative;
        background: var(--bg-tertiary);
        border-radius: 8px;
        padding: 16px;
      }

      /* === RESPONSIVE DESIGN === */
      @media (max-width: 768px) {
        .container {
          padding: 0 16px;
        }

        .grid-cols-4 {
          grid-template-columns: repeat(2, 1fr);
        }

        .grid-cols-6 {
          grid-template-columns: repeat(3, 1fr);
        }

        .navbar-brand {
          font-size: 1rem;
        }

        .stat-card {
          padding: 16px;
        }

        .stat-value {
          font-size: 1.5rem;
        }
      }

      @media (max-width: 480px) {
        .grid-cols-2,
        .grid-cols-3,
        .grid-cols-4,
        .grid-cols-6 {
          grid-template-columns: 1fr;
        }
      }

      /* === FORM ELEMENTS === */
      .form-select {
        background: var(--bg-tertiary);
        border: 1px solid var(--border-secondary);
        border-radius: 8px;
        padding: 8px 12px;
        color: var(--text-primary);
        font-size: 0.875rem;
        transition: var(--transition-normal);
        width: 100%;
      }

      .form-select:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 3px rgba(0, 212, 170, 0.1);
      }

      .form-select option {
        background: var(--bg-tertiary);
        color: var(--text-primary);
      }

      /* === STATUS BADGES === */
      .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      .status-new {
        background: rgba(0, 153, 255, 0.2);
        color: var(--accent-secondary);
      }

      .status-entry-hit {
        background: rgba(255, 165, 2, 0.2);
        color: var(--accent-warning);
      }

      .status-tp-hit {
        background: rgba(46, 213, 115, 0.2);
        color: var(--accent-success);
      }

      .status-sl-hit {
        background: rgba(255, 71, 87, 0.2);
        color: var(--accent-danger);
      }

      .status-expired {
        background: rgba(108, 114, 147, 0.2);
        color: var(--text-muted);
      }

      /* === TABLE STYLES === */
      .table-container {
        overflow-x: auto;
        border-radius: 8px;
        border: 1px solid var(--border-primary);
      }

      .signals-table {
        width: 100%;
        border-collapse: collapse;
        background: var(--bg-tertiary);
      }

      .signals-table th,
      .signals-table td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid var(--border-primary);
      }

      .signals-table th {
        background: var(--bg-secondary);
        font-weight: 600;
        color: var(--text-primary);
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .signals-table tbody tr {
        transition: var(--transition-normal);
      }

      .signals-table tbody tr:hover {
        background: var(--bg-hover);
      }

      .signals-table tbody tr:last-child td {
        border-bottom: none;
      }

      /* === ACTION BUTTONS === */
      .action-btn {
        padding: 4px 8px;
        border-radius: 4px;
        border: none;
        font-size: 0.75rem;
        cursor: pointer;
        transition: var(--transition-normal);
        margin-right: 4px;
      }

      .action-btn.view {
        background: var(--accent-secondary);
        color: white;
      }

      .action-btn.edit {
        background: var(--accent-warning);
        color: white;
      }

      .action-btn:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
      }

      /* === UTILITY CLASSES === */
      .gap-2 {
        gap: 0.5rem;
      }

      .gap-3 {
        gap: 0.75rem;
      }

      .bg-tertiary {
        background: var(--bg-tertiary);
      }

      .text-secondary {
        color: var(--text-secondary);
      }

      .text-muted {
        color: var(--text-muted);
      }

      /* === NOTIFICATION STYLES === */
      .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 16px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 1000;
        min-width: 300px;
        box-shadow: var(--shadow-lg);
        transition: var(--transition-normal);
      }

      .notification.success {
        background: var(--accent-success);
      }

      .notification.error {
        background: var(--accent-danger);
      }

      .notification.info {
        background: var(--accent-secondary);
      }

      .notification.warning {
        background: var(--accent-warning);
      }
    </style>
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar">
      <div class="container">
        <div class="navbar-content">
          <div class="navbar-brand">
            <i
              class="fas fa-chart-line"
              style="color: var(--accent-primary)"
            ></i>
            <span>Discord Bybit Signal Monitor</span>
            <div class="live-indicator" id="liveIndicator"></div>
            <span class="text-sm" style="color: var(--accent-success)"
              >Live</span
            >
          </div>
          <button class="btn btn-primary" onclick="refreshData()">
            <i class="fas fa-sync-alt"></i>
            <span>Odśwież</span>
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
      <!-- Time Filter Section -->
      <div class="card mb-6">
        <div class="card-body">
          <div class="flex items-center justify-between">
            <h3 class="font-semibold">Okres analizy</h3>
            <div class="flex gap-2" id="timeFilterButtons">
              <button
                class="btn btn-outline active"
                onclick="setTimeFilter(null)"
              >
                Wszystko
              </button>
              <button class="btn btn-outline" onclick="setTimeFilter(1)">
                24h
              </button>
              <button class="btn btn-outline" onclick="setTimeFilter(7)">
                7 dni
              </button>
              <button class="btn btn-outline" onclick="setTimeFilter(30)">
                30 dni
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Statistics Grid -->
      <div class="grid grid-cols-4 mb-6">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-signal"></i>
          </div>
          <div class="stat-value" id="totalSignals">-</div>
          <div class="stat-label">Wszystkie sygnały</div>
          <div class="stat-change positive" id="totalSignalsChange">
            <i class="fas fa-arrow-up"></i> +12% vs poprzedni okres
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <div class="stat-value" id="winRate">-</div>
          <div class="stat-label">Win Rate</div>
          <div class="stat-change positive" id="winRateChange">
            <i class="fas fa-arrow-up"></i> +2.4% vs poprzedni okres
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="stat-value" id="totalPnl">-</div>
          <div class="stat-label">Całkowity PnL</div>
          <div class="stat-change positive" id="totalPnlChange">
            <i class="fas fa-arrow-up"></i> +15.7% vs poprzedni okres
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">
            <i class="fas fa-chart-bar"></i>
          </div>
          <div class="stat-value" id="avgPnl">-</div>
          <div class="stat-label">Średni PnL</div>
          <div class="stat-change negative" id="avgPnlChange">
            <i class="fas fa-arrow-down"></i> -0.8% vs poprzedni okres
          </div>
        </div>
      </div>

      <!-- Advanced Metrics Grid -->
      <div class="grid grid-cols-6 mb-6">
        <div class="stat-card">
          <div class="stat-icon">
            <i class="fas fa-chart-area"></i>
          </div>
          <div class="stat-value" id="sharpeRatio">-</div>
          <div class="stat-label">Sharpe Ratio</div>
        </div>

        <div class="stat-card danger">
          <div class="stat-icon">
            <i class="fas fa-arrow-down"></i>
          </div>
          <div class="stat-value" id="maxDrawdown">-</div>
          <div class="stat-label">Max Drawdown</div>
        </div>

        <div class="stat-card success">
          <div class="stat-icon">
            <i class="fas fa-fire"></i>
          </div>
          <div class="stat-value" id="maxWins">-</div>
          <div class="stat-label">Max Wins</div>
        </div>

        <div class="stat-card danger">
          <div class="stat-icon">
            <i class="fas fa-snowflake"></i>
          </div>
          <div class="stat-value" id="maxLosses">-</div>
          <div class="stat-label">Max Losses</div>
        </div>

        <div class="stat-card info">
          <div class="stat-icon">
            <i class="fas fa-balance-scale"></i>
          </div>
          <div class="stat-value" id="profitFactor">-</div>
          <div class="stat-label">Profit Factor</div>
        </div>

        <div class="stat-card warning">
          <div class="stat-icon">
            <i class="fas fa-coins"></i>
          </div>
          <div class="stat-value" id="avgWin">-</div>
          <div class="stat-label">Avg Win</div>
        </div>
      </div>

      <!-- Charts Section -->
      <div class="grid grid-cols-2 mb-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-line"></i>
              Equity Curve (Krzywa kapitału)
            </h3>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="pnlChart" width="400" height="200"></canvas>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-pie"></i>
              Rozkład statusów sygnałów
            </h3>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Additional Charts -->
      <div class="grid grid-cols-2 mb-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-bar"></i>
              Rozkład PnL
            </h3>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas
                id="pnlDistributionChart"
                width="400"
                height="200"
              ></canvas>
            </div>
          </div>
        </div>

        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-coins"></i>
              Wydajność per para
            </h3>
          </div>
          <div class="card-body">
            <div class="chart-container">
              <canvas
                id="pairPerformanceChart"
                width="400"
                height="200"
              ></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Configuration Panel -->
      <div class="card mb-6">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-cog"></i>
            Panel konfiguracji
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-3">
            <div>
              <h4 class="font-semibold mb-2">Whitelist botów Discord:</h4>
              <div class="flex flex-col gap-2">
                <label class="flex items-center gap-2">
                  <input type="checkbox" checked />
                  <span>TradingBot#1234</span>
                </label>
                <label class="flex items-center gap-2">
                  <input type="checkbox" checked />
                  <span>SignalMaster#5678</span>
                </label>
                <label class="flex items-center gap-2">
                  <input type="checkbox" />
                  <span>CryptoSignals#9012</span>
                </label>
              </div>
            </div>
            <div>
              <h4 class="font-semibold mb-2">Parametry timeoutów:</h4>
              <div class="flex flex-col gap-2">
                <div>
                  <span class="text-sm text-secondary">Timeout sygnałów:</span>
                  <span class="font-bold">48h</span>
                </div>
                <div>
                  <span class="text-sm text-secondary">Sprawdzanie cen:</span>
                  <span class="font-bold">30s</span>
                </div>
                <div>
                  <span class="text-sm text-secondary">Aktywne kanały:</span>
                  <span class="font-bold">3</span>
                </div>
              </div>
            </div>
            <div>
              <h4 class="font-semibold mb-2">Status systemu:</h4>
              <div class="flex flex-col gap-2">
                <div class="flex items-center gap-2">
                  <span class="text-sm">Discord Bot:</span>
                  <span class="status-badge status-tp-hit">Online</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-sm">ByBit API:</span>
                  <span class="status-badge status-tp-hit">Connected</span>
                </div>
                <div class="flex items-center gap-2">
                  <span class="text-sm">Database:</span>
                  <span class="status-badge status-tp-hit">Active</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters and Export -->
      <div class="card mb-6">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-filter"></i>
            Filtry i eksport danych
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-4">
            <div>
              <label class="text-sm font-semibold mb-2 block">Status:</label>
              <select class="form-select" id="statusFilter">
                <option value="">Wszystkie</option>
                <option value="NEW">NEW</option>
                <option value="ENTRY_HIT">ENTRY HIT</option>
                <option value="TP_HIT">TP HIT</option>
                <option value="SL_HIT">SL HIT</option>
                <option value="EXPIRED">EXPIRED</option>
              </select>
            </div>
            <div>
              <label class="text-sm font-semibold mb-2 block">Para:</label>
              <select class="form-select" id="pairFilter">
                <option value="">Wszystkie</option>
              </select>
            </div>
            <div>
              <label class="text-sm font-semibold mb-2 block">Limit:</label>
              <select class="form-select" id="limitFilter">
                <option value="">Wszystkie</option>
                <option value="50">50</option>
                <option value="100">100</option>
                <option value="500">500</option>
              </select>
            </div>
            <div class="flex items-end">
              <button class="btn btn-primary" onclick="exportCSV()">
                <i class="fas fa-download"></i>
                Export CSV
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Live Monitoring Panel -->
      <div class="card mb-6">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-satellite-dish"></i>
            Panel monitorowania na żywo
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-2">
            <div>
              <h4 class="font-semibold mb-4">Ostatnie sygnały</h4>
              <div class="flex flex-col gap-3" id="recentSignals">
                <div
                  class="flex items-center justify-between p-3 bg-tertiary rounded"
                >
                  <div>
                    <span class="font-bold">BTCUSDT</span>
                    <span class="status-badge status-new">BUY</span>
                    <span class="text-sm text-secondary">@ 43,250</span>
                  </div>
                  <div class="text-sm text-muted">2 min temu</div>
                </div>
                <div
                  class="flex items-center justify-between p-3 bg-tertiary rounded"
                >
                  <div>
                    <span class="font-bold">ETHUSDT</span>
                    <span class="status-badge status-sl-hit">SELL</span>
                    <span class="text-sm text-secondary">@ 2,650</span>
                  </div>
                  <div class="text-sm text-muted">5 min temu</div>
                </div>
              </div>
            </div>
            <div>
              <h4 class="font-semibold mb-4">Aktywność systemu</h4>
              <div class="flex flex-col gap-3" id="systemActivity">
                <div class="flex items-center gap-3">
                  <div class="status-badge status-tp-hit">✓</div>
                  <span class="text-sm">Nowy sygnał zarejestrowany</span>
                  <span class="text-sm text-muted">14:32</span>
                </div>
                <div class="flex items-center gap-3">
                  <div class="status-badge status-tp-hit">✓</div>
                  <span class="text-sm">Take Profit osiągnięty</span>
                  <span class="text-sm text-muted">14:28</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Signals History Table -->
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="fas fa-history"></i>
            Historia sygnałów
            <span class="status-badge status-tp-hit" id="signalCount">6</span>
          </h3>
        </div>
        <div class="card-body">
          <div class="table-container">
            <table class="signals-table" id="signalsTable">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Para</th>
                  <th>Kierunek</th>
                  <th>Entry</th>
                  <th>TP</th>
                  <th>SL</th>
                  <th>Status</th>
                  <th>PnL</th>
                  <th>Data</th>
                  <th>Akcje</th>
                </tr>
              </thead>
              <tbody id="signalsTableBody">
                <!-- Data will be loaded here -->
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript -->
    <script>
      // === GLOBAL VARIABLES ===
      const socket = io();
      let pnlChart, statusChart, pnlDistributionChart, pairPerformanceChart;
      let currentSignals = [];
      let currentTimeFilter = null;

      // === INITIALIZATION ===
      document.addEventListener("DOMContentLoaded", function () {
        try {
          console.log("🚀 Initializing Production Dashboard...");
          initCharts();
          loadData();
          loadPairs();
          setupEventListeners();
          console.log("✅ Production Dashboard initialized successfully");
        } catch (error) {
          console.error("❌ Error initializing dashboard:", error);
          showNotification("Błąd inicjalizacji dashboardu", "error");
        }
      });

      // === CHART INITIALIZATION ===
      function initCharts() {
        // PnL Chart
        const pnlElement = document.getElementById("pnlChart");
        console.log("PnL Chart canvas element:", pnlElement);
        if (!pnlElement) {
          console.error("PnL Chart canvas element not found!");
          return;
        }
        const pnlCtx = pnlElement.getContext("2d");
        pnlChart = new Chart(pnlCtx, {
          type: "line",
          data: {
            labels: [],
            datasets: [
              {
                label: "Cumulative PnL",
                data: [],
                borderColor: "#00d4aa",
                backgroundColor: "rgba(0, 212, 170, 0.1)",
                tension: 0.4,
              },
            ],
          },
          options: {
            responsive: true,
            plugins: {
              legend: {
                labels: { color: "#ffffff" },
              },
            },
            scales: {
              x: { ticks: { color: "#a0a9c0" } },
              y: { ticks: { color: "#a0a9c0" } },
            },
          },
        });

        // Status Chart
        const statusElement = document.getElementById("statusChart");
        console.log("Status Chart canvas element:", statusElement);
        if (!statusElement) {
          console.error("Status Chart canvas element not found!");
          return;
        }
        const statusCtx = statusElement.getContext("2d");
        statusChart = new Chart(statusCtx, {
          type: "doughnut",
          data: {
            labels: ["TP Hit", "SL Hit", "Expired", "Entry Hit", "New"],
            datasets: [
              {
                data: [0, 0, 0, 0, 0],
                backgroundColor: [
                  "#2ed573",
                  "#ff4757",
                  "#6c7293",
                  "#ffa502",
                  "#0099ff",
                ],
              },
            ],
          },
          options: {
            responsive: true,
            plugins: {
              legend: {
                labels: { color: "#ffffff" },
              },
            },
          },
        });

        // PnL Distribution Chart
        const pnlDistElement = document.getElementById("pnlDistributionChart");
        console.log("PnL Distribution canvas element:", pnlDistElement);
        if (!pnlDistElement) {
          console.error("PnL Distribution canvas element not found!");
          return;
        }
        const pnlDistCtx = pnlDistElement.getContext("2d");
        pnlDistributionChart = new Chart(pnlDistCtx, {
          type: "bar",
          data: {
            labels: [],
            datasets: [
              {
                label: "Frequency",
                data: [],
                backgroundColor: "#00d4aa",
              },
            ],
          },
          options: {
            responsive: true,
            plugins: {
              legend: {
                labels: { color: "#ffffff" },
              },
            },
            scales: {
              x: { ticks: { color: "#a0a9c0" } },
              y: { ticks: { color: "#a0a9c0" } },
            },
          },
        });

        // Pair Performance Chart
        const pairElement = document.getElementById("pairPerformanceChart");
        console.log("Pair Performance canvas element:", pairElement);
        if (!pairElement) {
          console.error("Pair Performance canvas element not found!");
          return;
        }
        const pairCtx = pairElement.getContext("2d");
        pairPerformanceChart = new Chart(pairCtx, {
          type: "bar",
          data: {
            labels: [],
            datasets: [
              {
                label: "Total PnL",
                data: [],
                backgroundColor: "#0099ff",
              },
            ],
          },
          options: {
            responsive: true,
            plugins: {
              legend: {
                labels: { color: "#ffffff" },
              },
            },
            scales: {
              x: { ticks: { color: "#a0a9c0" } },
              y: { ticks: { color: "#a0a9c0" } },
            },
          },
        });
      }

      // === DATA LOADING FUNCTIONS ===
      async function loadData() {
        try {
          const [statsResponse, signalsResponse, pnlResponse, pnlDistResponse] =
            await Promise.all([
              fetch(
                `/api/statistics${
                  currentTimeFilter ? `?days=${currentTimeFilter}` : ""
                }`
              ),
              fetch("/api/signals?limit=100"),
              fetch(
                `/api/pnl-chart${
                  currentTimeFilter ? `?days=${currentTimeFilter}` : ""
                }`
              ),
              fetch("/api/pnl-distribution"),
            ]);

          const stats = await statsResponse.json();
          const signals = await signalsResponse.json();
          const pnlData = await pnlResponse.json();
          const pnlDistData = await pnlDistResponse.json();

          console.log("Updating dashboard with data:", {
            stats: stats,
            signals: signals.length,
            pnlData: pnlData.length,
            pnlDistData: pnlDistData.length,
          });

          updateStatistics(stats);
          updateSignalsTable(signals);
          updatePnlChart(pnlData);
          updateStatusChart(stats);
          updatePnlDistributionChart(pnlDistData);
          updatePairPerformanceChart(stats.pair_stats || []);

          currentSignals = signals;
        } catch (error) {
          console.error("Error loading data:", error);
          showNotification("Błąd ładowania danych", "error");
        }
      }

      async function loadPairs() {
        try {
          const response = await fetch("/api/pairs");
          const pairs = await response.json();

          const pairSelect = document.getElementById("pairFilter");
          pairs.forEach((pair) => {
            const option = document.createElement("option");
            option.value = pair;
            option.textContent = pair;
            pairSelect.appendChild(option);
          });
        } catch (error) {
          console.error("Error loading pairs:", error);
        }
      }

      // === UPDATE FUNCTIONS ===
      function updateStatistics(stats) {
        document.getElementById("totalSignals").textContent =
          stats.total_signals || 0;
        document.getElementById("winRate").textContent = `${(
          stats.win_rate || 0
        ).toFixed(1)}%`;
        document.getElementById("totalPnl").textContent = `${(
          (stats.total_pnl || 0) * 100
        ).toFixed(2)}%`;
        document.getElementById("avgPnl").textContent = `${(
          (stats.avg_pnl || 0) * 100
        ).toFixed(2)}%`;

        // Advanced metrics
        document.getElementById("sharpeRatio").textContent = (
          stats.sharpe_ratio || 0
        ).toFixed(2);
        document.getElementById("maxDrawdown").textContent = `${(
          (stats.max_drawdown || 0) * 100
        ).toFixed(2)}%`;
        document.getElementById("maxWins").textContent =
          stats.max_consecutive_wins || 0;
        document.getElementById("maxLosses").textContent =
          stats.max_consecutive_losses || 0;
        document.getElementById("profitFactor").textContent = (
          stats.profit_factor || 0
        ).toFixed(2);
        document.getElementById("avgWin").textContent = `${(
          (stats.avg_win || 0) * 100
        ).toFixed(2)}%`;
      }

      function updateSignalsTable(signals) {
        const tbody = document.getElementById("signalsTableBody");
        tbody.innerHTML = "";

        signals.forEach((signal) => {
          const row = document.createElement("tr");
          row.innerHTML = `
            <td>${signal.id}</td>
            <td><strong>${signal.pair}</strong></td>
            <td><span class="status-badge status-${signal.side.toLowerCase()}">${
            signal.side
          }</span></td>
            <td>${signal.entry}</td>
            <td>${signal.tp}</td>
            <td>${signal.sl}</td>
            <td><span class="status-badge status-${signal.status
              .toLowerCase()
              .replace("_", "-")}">${signal.status}</span></td>
            <td class="${
              signal.pnl !== null && signal.pnl > 0
                ? "text-success"
                : signal.pnl !== null && signal.pnl < 0
                ? "text-danger"
                : ""
            }">${
            signal.pnl !== null &&
            signal.status !== "NEW" &&
            signal.status !== "ENTRY_HIT"
              ? `${(signal.pnl * 100).toFixed(2)}%`
              : "-"
          }</td>
            <td>${
              signal.timestamp
                ? new Date(signal.timestamp).toLocaleDateString()
                : "-"
            }</td>
            <td>
              <button class="action-btn view" onclick="viewSignal(${
                signal.id
              })">
                <i class="fas fa-eye"></i>
              </button>
              <button class="action-btn edit" onclick="editSignal(${
                signal.id
              })">
                <i class="fas fa-edit"></i>
              </button>
            </td>
          `;
          tbody.appendChild(row);
        });

        document.getElementById("signalCount").textContent = signals.length;
      }

      function updatePnlChart(data) {
        if (!data || data.length === 0) {
          console.log("No PnL chart data available");
          return;
        }

        console.log("Updating PnL chart with data:", data);

        const labels = data.map((item) => item.date);
        const values = data.map((item) => (item.cumulative_pnl || 0) * 100); // Convert to percentage

        pnlChart.data.labels = labels;
        pnlChart.data.datasets[0].data = values;
        pnlChart.update();
      }

      function updateStatusChart(stats) {
        const data = [
          stats.tp_hits || 0,
          stats.sl_hits || 0,
          stats.timeouts || 0,
          stats.entry_hit_signals || 0,
          stats.new_signals || 0,
        ];

        statusChart.data.datasets[0].data = data;
        statusChart.update();
      }

      function updatePnlDistributionChart(data) {
        console.log("Updating PnL Distribution chart with data:", data);
        if (!data || data.length === 0) {
          console.log("No PnL distribution data available");
          return;
        }

        const labels = data.map(
          (item) =>
            `${(item.range_start * 100).toFixed(1)}% - ${(
              item.range_end * 100
            ).toFixed(1)}%`
        );
        const values = data.map((item) => item.count);

        console.log("PnL Distribution labels:", labels);
        console.log("PnL Distribution values:", values);

        pnlDistributionChart.data.labels = labels;
        pnlDistributionChart.data.datasets[0].data = values;
        pnlDistributionChart.update();
      }

      function updatePairPerformanceChart(pairStats) {
        console.log("Updating Pair Performance chart with data:", pairStats);
        if (!pairStats || pairStats.length === 0) {
          console.log("No pair performance data available");
          return;
        }

        const labels = pairStats.map((item) => item.pair);
        const values = pairStats.map((item) => (item.total_pnl || 0) * 100); // Convert to percentage

        console.log("Pair Performance labels:", labels);
        console.log("Pair Performance values:", values);

        pairPerformanceChart.data.labels = labels;
        pairPerformanceChart.data.datasets[0].data = values;
        pairPerformanceChart.update();
      }

      // === EVENT HANDLERS ===
      function setTimeFilter(days) {
        currentTimeFilter = days;

        // Update button states
        document.querySelectorAll("#timeFilterButtons .btn").forEach((btn) => {
          btn.classList.remove("active");
        });
        event.target.classList.add("active");

        loadData();
      }

      function setupEventListeners() {
        // Filter change handlers
        document
          .getElementById("statusFilter")
          .addEventListener("change", filterSignals);
        document
          .getElementById("pairFilter")
          .addEventListener("change", filterSignals);
        document
          .getElementById("limitFilter")
          .addEventListener("change", filterSignals);
      }

      function filterSignals() {
        const statusFilter = document.getElementById("statusFilter").value;
        const pairFilter = document.getElementById("pairFilter").value;
        const limitFilter = document.getElementById("limitFilter").value;

        let filteredSignals = [...currentSignals];

        if (statusFilter) {
          filteredSignals = filteredSignals.filter(
            (signal) => signal.status === statusFilter
          );
        }

        if (pairFilter) {
          filteredSignals = filteredSignals.filter(
            (signal) => signal.pair === pairFilter
          );
        }

        if (limitFilter) {
          filteredSignals = filteredSignals.slice(0, parseInt(limitFilter));
        }

        updateSignalsTable(filteredSignals);
      }

      // === UTILITY FUNCTIONS ===
      function refreshData() {
        showNotification("Odświeżanie danych...", "info");
        loadData();
      }

      function exportCSV() {
        const csvContent = generateCSV(currentSignals);
        downloadCSV(csvContent, "signals.csv");
        showNotification("Eksport CSV zakończony", "success");
      }

      function generateCSV(signals) {
        const headers = [
          "ID",
          "Para",
          "Kierunek",
          "Entry",
          "TP",
          "SL",
          "Status",
          "PnL",
          "Data",
        ];
        const rows = signals.map((signal) => [
          signal.id,
          signal.pair,
          signal.side,
          signal.entry,
          signal.tp,
          signal.sl,
          signal.status,
          signal.pnl || "",
          signal.timestamp || "",
        ]);

        return [headers, ...rows].map((row) => row.join(",")).join("\n");
      }

      function downloadCSV(content, filename) {
        const blob = new Blob([content], { type: "text/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        a.click();
        window.URL.revokeObjectURL(url);
      }

      function showNotification(message, type = "info") {
        const notification = document.createElement("div");
        notification.className = `notification ${type}`;
        notification.innerHTML = `
          <div class="flex items-center justify-between">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; cursor: pointer;">
              <i class="fas fa-times"></i>
            </button>
          </div>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
          if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
          }
        }, 5000);
      }

      function viewSignal(id) {
        showNotification(`Wyświetlanie sygnału #${id}`, "info");
      }

      function editSignal(id) {
        showNotification(`Edycja sygnału #${id}`, "info");
      }

      // === WEBSOCKET HANDLERS ===
      socket.on("connect", function () {
        console.log("Connected to server");
        document.getElementById("liveIndicator").style.background =
          "var(--accent-success)";
      });

      socket.on("disconnect", function () {
        console.log("Disconnected from server");
        document.getElementById("liveIndicator").style.background =
          "var(--accent-danger)";
      });

      socket.on("new_signal", function (data) {
        showNotification(`Nowy sygnał: ${data.pair} ${data.side}`, "success");
        loadData(); // Refresh data
      });

      socket.on("signal_update", function (data) {
        showNotification(
          `Aktualizacja sygnału: ${data.pair} - ${data.status}`,
          "info"
        );
        loadData(); // Refresh data
      });
    </script>
  </body>
</html>
